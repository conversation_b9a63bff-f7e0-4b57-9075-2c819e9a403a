/* Minimal style sheet for the HTML output of Docutils.                    */
/*                                                                         */
/* :Author: <PERSON><PERSON><PERSON>, based on html4css1.css by <PERSON>          */
/* :Id: $Id: minimal.css 9545 2024-02-17 10:37:56Z milde $                                                               */
/* :Copyright: © 2015, 2021 Günter Milde.                                  */
/* :License: Released under the terms of the `2-Clause BSD license`_,      */
/*    in short:                                                            */
/*                                                                         */
/*    Copying and distribution of this file, with or without modification, */
/*    are permitted in any medium without royalty provided the copyright   */
/*    notice and this notice are preserved.                                */
/*                                                                         */
/*    This file is offered as-is, without any warranty.                    */
/*                                                                         */
/* .. _2-Clause BSD license: http://www.spdx.org/licenses/BSD-2-Clause     */

/* This CSS3 stylesheet defines rules for Docutils elements without        */
/* HTML equivalent. It is required to make the document semantics visible. */
/*                                                                         */
/* .. _validates: http://jigsaw.w3.org/css-validator/validator$link        */

/* titles */
p.topic-title,
p.admonition-title,
p.system-message-title {
  font-weight: bold;
}
p.sidebar-title,
p.rubric {
  font-weight: bold;
  font-size: larger;
}
p.rubric {
  color: maroon;
}
p.subtitle,
p.section-subtitle,
p.sidebar-subtitle {
  font-weight: bold;
  margin-top: -0.5em;
}
h1 + p.subtitle {
  font-size: 1.6em;
}
a.toc-backref {
  color: inherit;
  text-decoration: none;
}

/* Warnings, Errors */
.system-messages h2,
.system-message-title,
pre.problematic,
span.problematic {
  color: red;
}

/* Inline Literals */
.docutils.literal {
  font-family: monospace;
  white-space: pre-wrap;
}
/* do not wrap at hyphens and similar: */
.literal > span.pre { white-space: nowrap; }

/* keep line-breaks (\n) visible */
.pre-wrap { white-space: pre-wrap; }

/* Lists */

/* compact and simple lists: no margin between items */
.simple  li, .simple  ul, .simple  ol,
.compact li, .compact ul, .compact ol,
.simple  > li p, dl.simple  > dd,
.compact > li p, dl.compact > dd {
  margin-top: 0;
  margin-bottom: 0;
}
/* Nested Paragraphs */
p:first-child { margin-top: 0; }
p:last-child { margin-bottom: 0; }
details > p:last-child { margin-bottom: 1em; }

/* Table of Contents */
.contents ul.auto-toc { /* section numbers present */
  list-style-type: none;
}

/* Enumerated Lists */
ol.arabic     { list-style: decimal }
ol.loweralpha { list-style: lower-alpha }
ol.upperalpha { list-style: upper-alpha }
ol.lowerroman { list-style: lower-roman }
ol.upperroman { list-style: upper-roman }

/* Definition Lists and Derivatives */
dt .classifier { font-style: italic }
dt .classifier:before {
  font-style: normal;
  margin: 0.5em;
  content: ":";
}
/* Field Lists and similar */
/* bold field name, content starts on the same line */
dl.field-list,
dl.option-list,
dl.docinfo {
  display: flow-root;
}
dl.field-list > dt,
dl.option-list > dt,
dl.docinfo > dt {
  font-weight: bold;
  clear: left;
  float: left;
  margin: 0;
  padding: 0;
  padding-right: 0.25em;
}
/* Offset for field content (corresponds to the --field-name-limit option) */
dl.field-list > dd,
dl.option-list > dd,
dl.docinfo > dd {
  margin-left:  9em; /* ca. 14 chars in the test examples, fit all Docinfo fields */
}
/* start nested lists on new line */
dd > dl:first-child,
dd > ul:first-child,
dd > ol:first-child {
  clear: left;
}
/* start field-body on a new line after long field names */
dl.field-list > dd > *:first-child,
dl.option-list > dd > *:first-child
{
  display: inline-block;
  width: 100%;
  margin: 0;
}

/* Bibliographic Fields (docinfo) */
dl.docinfo pre.address {
  font: inherit;
  margin: 0.5em 0;
}
dl.docinfo > dd.authors > p { margin: 0; }

/* Option Lists */
dl.option-list > dt { font-weight: normal; }
span.option { white-space: nowrap; }

/* Footnotes and Citations  */

.footnote, .citation { margin: 1em 0; } /* default paragraph skip (Firefox) */
/* hanging indent */
.citation { padding-left: 2em; }
.footnote { padding-left: 1.7em; }
.footnote.superscript { padding-left: 1.0em; }
.citation > .label { margin-left: -2em; }
.footnote > .label { margin-left: -1.7em; }
.footnote.superscript > .label { margin-left: -1.0em; }

.footnote > .label + *,
.citation > .label + * {
  display: inline-block;
  margin-top: 0;
  vertical-align: top;
}
.footnote > .backrefs + *,
.citation > .backrefs + * {
  margin-top: 0;
}
.footnote > .label + p, .footnote > .backrefs + p,
.citation > .label + p, .citation > .backrefs + p {
  display: inline;
  vertical-align: inherit;
}

.backrefs { user-select: none; }
.backrefs > a { font-style: italic; }

/* superscript footnotes */
a[role="doc-noteref"].superscript,
.footnote.superscript > .label,
.footnote.superscript > .backrefs {
  vertical-align: super;
  font-size: smaller;
  line-height: 1;
}
a[role="doc-noteref"].superscript > .fn-bracket,
.footnote.superscript > .label > .fn-bracket {
  /* hide brackets in display but leave for copy/paste */
  display: inline-block;
  width: 0;
  overflow: hidden;
}
[role="doc-noteref"].superscript + [role="doc-noteref"].superscript {
  padding-left: 0.15em; /* separate consecutive footnote references */
  /* TODO: unfortunately, "+" also selects with text between the references. */
}

/* Alignment */
.align-left   {
  text-align: left;
  margin-right: auto;
}
.align-center {
  text-align: center;
  margin-left: auto;
  margin-right: auto;
}
.align-right  {
  text-align: right;
  margin-left: auto;
}
.align-top    { vertical-align: top; }
.align-middle { vertical-align: middle; }
.align-bottom { vertical-align: bottom; }

/* reset inner alignment in figures and tables */
figure.align-left, figure.align-right,
table.align-left, table.align-center, table.align-right {
  text-align: inherit;
}

/* Text Blocks */
.topic { margin: 1em 2em; }
.sidebar,
.admonition,
.system-message {
  margin: 1em 2em;
  border: thin solid;
  padding: 0.5em 1em;
}
div.line-block { display: block; }
div.line-block div.line-block, pre { margin-left: 2em; }

/* Code line numbers: dropped when copying text from the page */
pre.code .ln { display: none; }
pre.code code:before {
  content: attr(data-lineno); /* …, none) fallback not supported by any browser */
  color: gray;
}

/* Tables */
table {
  border-collapse: collapse;
}
td, th {
  border: thin solid silver;
  padding: 0 1ex;
}
.borderless td, .borderless th {
  border: 0;
  padding: 0;
  padding-right: 0.5em /* separate table cells */
}

table > caption, figcaption {
  text-align: left;
  margin-top: 0.2em;
  margin-bottom: 0.2em;
}
table.captionbelow {
  caption-side: bottom;
}

/* MathML (see "math.css" for --math-output=HTML) */
math .boldsymbol { font-weight: bold; }
math.boxed, math .boxed {padding: 0.25em; border: thin solid; }
/* style table similar to AMS "align" or "aligned" environment: */
mtable.cases > mtr > mtd { text-align: left; }
mtable.ams-align > mtr > mtd { padding-left: 0; padding-right: 0; }
mtable.ams-align > mtr > mtd:nth-child(2n) { text-align: left; }
mtable.ams-align > mtr > mtd:nth-child(2n+1) { text-align: right; }
mtable.ams-align > mtr > mtd:nth-child(2n+3) { padding-left: 2em; }
.mathscr mi, mi.mathscr {
  font-family: STIX, XITSMathJax_Script, rsfs10,
               "Asana Math", Garamond, cursive;
}

/* Document Header and Footer */
header { border-bottom: 1px solid black; }
footer { border-top: 1px solid black; }

/* Images are block-level by default in Docutils */
/* New HTML5 block elements: set display for older browsers */
img, svg, header, footer, main, aside, nav, section, figure, video, details {
  display: block;
}
svg { width: auto; height: auto; }  /* enable scaling of SVG images */
/* inline images */
p img, p svg, p video { display: inline; }
