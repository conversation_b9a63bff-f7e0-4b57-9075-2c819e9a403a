# $Id: null.py 4564 2006-05-21 20:44:42Z w<PERSON><PERSON> $
# Author: <PERSON> <<EMAIL>>
# Copyright: This module has been placed in the public domain.

"""A do-nothing parser."""

from docutils import parsers


class Parser(parsers.Parser):

    """A do-nothing parser."""

    supported = ('null',)

    config_section = 'null parser'
    config_section_dependencies = ('parsers',)

    def parse(self, inputstring, document):
        pass
