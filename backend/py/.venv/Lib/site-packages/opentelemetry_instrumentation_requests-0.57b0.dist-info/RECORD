opentelemetry/instrumentation/requests/__init__.py,sha256=QBULt44y1MZmeYj0PZowZY5XQhX85Rjj4ax9r1490ow,17899
opentelemetry/instrumentation/requests/package.py,sha256=84lK70NyCoRefASXKjU5f4byJhf5qWDL6IdYjch-UTM,679
opentelemetry/instrumentation/requests/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
opentelemetry/instrumentation/requests/version.py,sha256=Q5HertrmLRVt4tr9uzo3rMgUo0jPRhv7CHMT4epUldg,608
opentelemetry_instrumentation_requests-0.57b0.dist-info/INSTALLER,sha256=5hhM4Q4mYTT9z6QB6PGpUAW81PGNFrYrdXMj4oM_6ak,2
opentelemetry_instrumentation_requests-0.57b0.dist-info/METADATA,sha256=KL-_oDAcYPK0gFmhv5oHVFYxnOpx6ycdcjhWqo0DPBw,2620
opentelemetry_instrumentation_requests-0.57b0.dist-info/RECORD,,
opentelemetry_instrumentation_requests-0.57b0.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
opentelemetry_instrumentation_requests-0.57b0.dist-info/WHEEL,sha256=qtCwoSJWgHk21S1Kb4ihdzI2rlJ1ZKaIurTj_ngOhyQ,87
opentelemetry_instrumentation_requests-0.57b0.dist-info/entry_points.txt,sha256=w_cFVp9h9IXzWm2YeBaOEUANSn9iuUlNAl0QC3PDf94,100
opentelemetry_instrumentation_requests-0.57b0.dist-info/licenses/LICENSE,sha256=xx0jnfkXJvxRnG63LTGOxlggYnIysveWIZ6H3PNdCrQ,11357
